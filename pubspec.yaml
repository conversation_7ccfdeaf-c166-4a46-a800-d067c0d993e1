name: odigo_display
description: "A new Flutter project."

# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 11.0.0+1

# Uploaded Version to DMS
# version: 1.0.0+2

environment:
  sdk: '>=3.3.0 <4.0.0'

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter


  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.2
  freezed_annotation: 2.4.1
  json_annotation: 4.8.1
  injectable: 2.3.1
  get_it: 7.6.4
  kiosk_mode:
  flutter_archive:
  #State Management
  flutter_riverpod: 2.4.8

  #Storage and permission
  path_provider: 2.1.1
  cached_network_image: 3.3.0
  #  flutter_svg: 2.0.9
  flutter_svg_custom:
    git:
      url: https://github.com/Mann97/flutter_svg_custom
  lottie: 1.4.2
  #  open_settings_plus: ^0.3.3

  #Localization and screen
  easy_localization:
  flutter_screenutil: 5.9.0
  responsive_builder: 0.7.0

  #Network
  dio: 5.3.3
  flutter_offline: 3.0.0


  #Other Widgets
  pin_code_fields: 8.0.1
  csv:

  #Device Info
  device_info_plus:
  carousel_slider:
  package_info_plus:


  fluttertoast:


  chewie: 1.8.0
  video_player: 2.8.6
  objectbox: 4.0.2
  objectbox_flutter_libs: 4.0.2

  get_thumbnail_video:
  flutter_downloader: ^1.11.7
  loading_animation_widget: ^1.2.1
  #  logcat_monitor: ^0.0.6
  alarm: ^3.1.5
  volume_controller: ^2.0.8
  flutter_colorpicker: ^1.1.0
  collection:
  hive: 2.2.3
  hive_flutter: 1.1.0
  syncfusion_flutter_gauges: ^21.2.10
  image:
  tutorial_coach_mark: 1.2.11
  open_file:

  kody_enc_dec:
    git:
      url: https://github.com/projectKody/kody_enc_dec
  zoom_widget: ^2.0.1

dependency_overrides:
  firebase_core_platform_interface:
  intl: ^0.20.2
  win32: 5.5.0
  shared_preferences_android: ^2.4.10
  video_player_platform_interface: 6.2.3

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^2.0.0
  build_runner:
  freezed: 2.4.3
  json_serializable: 6.7.1
  injectable_generator: 2.4.0
  flutter_gen_runner: 5.3.2
  lints: 2.0.1
  socket_io_client: ^2.0.3+1
  objectbox_generator: any

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

flutter_gen:
  version: 5.3.2
  output: lib/ui/utils/theme
  integrations:
    flutter_svg: true
    flutter_custom_svg: true
    lottie: true

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/json/
    - assets/fonts/
    - assets/svgs/
    - lang/en.json
    - lang/ar.json
    - assets/animation/

  fonts:
    - family: Outfit
      fonts:
        - asset: assets/fonts/Outfit/Outfit-Black.ttf
        - asset: assets/fonts/Outfit/Outfit-Bold.ttf
        - asset: assets/fonts/Outfit/Outfit-ExtraBold.ttf
        - asset: assets/fonts/Outfit/Outfit-ExtraLight.ttf
        - asset: assets/fonts/Outfit/Outfit-Light.ttf
        - asset: assets/fonts/Outfit/Outfit-Medium.ttf
        - asset: assets/fonts/Outfit/Outfit-Regular.ttf
        - asset: assets/fonts/Outfit/Outfit-SemiBold.ttf
        - asset: assets/fonts/Outfit/Outfit-Thin.ttf

    - family: Manrope
      fonts:
        - asset: assets/fonts/Manrope/Manrope-Bold.ttf
        - asset: assets/fonts/Manrope/Manrope-ExtraBold.ttf
        - asset: assets/fonts/Manrope/Manrope-ExtraLight.ttf
        - asset: assets/fonts/Manrope/Manrope-Light.ttf
        - asset: assets/fonts/Manrope/Manrope-Medium.ttf
        - asset: assets/fonts/Manrope/Manrope-Regular.ttf
        - asset: assets/fonts/Manrope/Manrope-SemiBold.ttf
