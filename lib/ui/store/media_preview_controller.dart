import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:injectable/injectable.dart';
import 'package:odigo_display/framework/dependency_injection/inject.dart';

final mediaPreviewController = ChangeNotifierProvider(
  (ref) => getIt<MediaPreviewController>(),
);

@injectable
class MediaPreviewController extends ChangeNotifier {
  String? mediaPath = '';
  int currentMediaIndex = 0;
  List<String?>? mediaList;

  ///Initialize media list and reset index
  void initializeMediaList(List<String?>? list) {
    mediaList = list;
    currentMediaIndex = 0;
    notifyListeners();
  }

  ///Navigate to next media
  void nextMedia() {
    if (mediaList != null && currentMediaIndex < mediaList!.length - 1) {
      currentMediaIndex++;
      notifyListeners();
    }
  }

  ///Navigate to previous media
  void previousMedia() {
    if (currentMediaIndex > 0) {
      currentMediaIndex--;
      notifyListeners();
    }
  }

  ///Set current media index
  void setCurrentMediaIndex(int index) {
    if (mediaList != null && index >= 0 && index < mediaList!.length) {
      currentMediaIndex = index;
      notifyListeners();
    }
  }

  ///Get current media path
  String? getCurrentMediaPath() {
    if (mediaList != null && currentMediaIndex < mediaList!.length) {
      return mediaList![currentMediaIndex];
    }
    return null;
  }

  ///Check if there are multiple media items
  bool hasMultipleMedia() {
    return mediaList != null && mediaList!.length > 1;
  }

  ///Get total media count
  int getMediaCount() {
    return mediaList?.length ?? 0;
  }

  ///Dispose Controller
  void disposeController({bool isNotify = false}) {
    isLoading = false;
    currentMediaIndex = 0;
    mediaList = null;
    mediaPath = '';

    if (isNotify) {
      notifyListeners();
    }
  }

  updateWidget() {
    notifyListeners();
  }

  /*
  /// ---------------------------- Api Integration ---------------------------------///
   */

  ///Progress Indicator
  bool isLoading = false;

  void updateLoadingStatus(bool value) {
    isLoading = value;
    notifyListeners();
  }
}
