import 'dart:io';

import 'package:chewie/chewie.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:odigo_display/framework/controller/display_ads/display_ads_controller.dart';
import 'package:odigo_display/framework/utils/extension/context_extension.dart';
import 'package:odigo_display/framework/utils/extension/extension.dart';
import 'package:odigo_display/ui/routing/stack.dart';
import 'package:odigo_display/ui/store/media_preview_controller.dart';
import 'package:odigo_display/ui/utils/const/app_constants.dart';
import 'package:odigo_display/ui/utils/helper/base_widget.dart';
import 'package:odigo_display/ui/utils/theme/app_colors.dart';
import 'package:odigo_display/ui/utils/theme/theme.dart';
import 'package:video_player/video_player.dart';

class MediaPreviewMobile extends ConsumerStatefulWidget {
  final bool isImage;
  final List<String?>? mediaList;

  const MediaPreviewMobile({Key? key, required this.mediaList, required this.isImage}) : super(key: key);

  @override
  ConsumerState<MediaPreviewMobile> createState() => _MediaPreviewMobileState();
}

class _MediaPreviewMobileState extends ConsumerState<MediaPreviewMobile> with BaseConsumerStatefulWidget {
  ChewieController? chewieController;
  String? image;
  VideoPlayerController? videoPlayerController;
  PageController? pageController;
  Map<int, ChewieController> chewieControllers = {};
  Map<int, VideoPlayerController> videoPlayerControllers = {};

  ///Init Override
  @override
  void initState() {
    super.initState();
    pageController = PageController();
    SchedulerBinding.instance.addPostFrameCallback((timeStamp) async {
      final mediaPreviewWatch = ref.read(mediaPreviewController);
      hideBottomMenu();

      // Initialize media list in controller
      mediaPreviewWatch.initializeMediaList(widget.mediaList);

      // Load first media item
      if (widget.mediaList != null && widget.mediaList!.isNotEmpty) {
        await loadMediaAtIndex(0);
      }
    });
  }

  ///Dispose Override
  @override
  void dispose() {
    super.dispose();

    // Dispose all video controllers
    for (var controller in videoPlayerControllers.values) {
      controller.dispose();
    }

    // Dispose all chewie controllers
    for (var controller in chewieControllers.values) {
      controller.dispose();
    }

    videoPlayerController?.dispose();
    chewieController?.dispose();
    pageController?.dispose();

    videoPlayerControllers.clear();
    chewieControllers.clear();
  }

  ///Build Override
  @override
  Widget buildPage(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.black,
      body: _bodyWidget(),
    );
  }

  ///Body Widget
  Widget _bodyWidget() {
    final mediaPreviewWatch = ref.watch(mediaPreviewController);
    return Stack(
      children: [
        (widget.mediaList != null && widget.mediaList!.isNotEmpty)
            ? PageView.builder(
                controller: pageController,
                itemCount: widget.mediaList!.length,
                onPageChanged: (index) {
                  mediaPreviewWatch.setCurrentMediaIndex(index);
                  loadMediaAtIndex(index);
                },
                itemBuilder: (context, index) {
                  return _buildMediaItem(index);
                },
              )
            : const SizedBox(),
        // Back button
        Positioned(
          top: context.height * 0.03,
          left: 50.w,
          child: InkWell(
            onTap: () async {
              // Dispose all controllers
              for (var controller in videoPlayerControllers.values) {
                controller.dispose();
              }
              for (var controller in chewieControllers.values) {
                controller.dispose();
              }
              chewieController?.dispose();
              videoPlayerController?.dispose();

              Future.delayed(const Duration(milliseconds: 500), () {
                ref.read(navigationStackController).pop();
              });
            },
            child: Container(
              padding: EdgeInsets.all(10.r),
              decoration: const BoxDecoration(color: AppColors.white, shape: BoxShape.circle),
              child: Icon(CupertinoIcons.back, color: AppColors.black, size: 45.r),
            ),
          ),
        ),

        // Media counter (show only if multiple media items)
        if (mediaPreviewWatch.hasMultipleMedia())
          Positioned(
            top: context.height * 0.03,
            right: 50.w,
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
              decoration: BoxDecoration(
                color: AppColors.black.withOpacity(0.7),
                borderRadius: BorderRadius.circular(20.r),
              ),
              child: Text(
                '${mediaPreviewWatch.currentMediaIndex + 1}/${mediaPreviewWatch.getMediaCount()}',
                style: TextStyle(
                  color: AppColors.white,
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
      ],
    );
  }

  /// Build media item for specific index
  Widget _buildMediaItem(int index) {
    if (widget.mediaList == null || index >= widget.mediaList!.length) {
      return const SizedBox();
    }

    final mediaPath = widget.mediaList![index];
    if (mediaPath == null || mediaPath.isEmpty) {
      return const SizedBox();
    }

    if (widget.isImage) {
      // For images, show the decrypted file path
      return Image.file(
        File(mediaPath.decryptedFilePath),
        height: context.height,
        width: context.width,
        fit: BoxFit.contain,
        errorBuilder: (context, error, stackTrace) {
          return Center(
            child: Text(
              'Error loading image',
              style: TextStyle(color: AppColors.white, fontSize: 16.sp),
            ),
          );
        },
      ).alignAtCenter();
    } else {
      // For videos, use the chewie controller for this index
      final chewieCtrl = chewieControllers[index];
      if (chewieCtrl != null) {
        return Chewie(controller: chewieCtrl);
      } else {
        return const Center(
          child: CircularProgressIndicator(
            color: AppColors.white,
          ),
        );
      }
    }
  }

  /// Load media at specific index
  Future<void> loadMediaAtIndex(int index) async {
    if (widget.mediaList == null || index >= widget.mediaList!.length) {
      return;
    }

    final mediaPath = widget.mediaList![index];
    if (mediaPath == null || mediaPath.isEmpty) {
      return;
    }

    final displayAdsScreenWatch = ref.read(displayAdsController);
    final mediaPreviewWatch = ref.read(mediaPreviewController);

    try {
      // Decrypt the file
      await displayAdsScreenWatch.decryptFile(mediaPath);
      final decryptedPath = mediaPath.decryptedFilePath;

      if (widget.isImage) {
        // For images, just update the media path
        mediaPreviewWatch.mediaPath = decryptedPath;
        mediaPreviewWatch.updateWidget();
      } else {
        // For videos, create video and chewie controllers if not already created
        if (!videoPlayerControllers.containsKey(index)) {
          await loadVideoAtIndex(index, decryptedPath);
        }
      }
    } catch (e) {
      showLog('Error loading media at index $index: $e');
    }
  }

  /// Load video at specific index
  Future<void> loadVideoAtIndex(int index, String url) async {
    final mediaPreviewWatch = ref.read(mediaPreviewController);

    // Create video player controller
    final videoController = VideoPlayerController.file(File(url));
    await videoController.initialize();

    // Create chewie controller
    final chewieCtrl = ChewieController(
      videoPlayerController: videoController,
      autoPlay: true,
      looping: false,
      customControls: const Offstage(),
      allowFullScreen: true,
      fullScreenByDefault: true,
    );

    // Store controllers
    videoPlayerControllers[index] = videoController;
    chewieControllers[index] = chewieCtrl;

    // Set looping
    videoController.setLooping(false);

    // Add listener for video end
    videoController.addListener(() {
      if (videoController.value.duration == videoController.value.position) {
        showLog('Video Ended at index $index');
        // Optionally auto-advance to next video
        if (index < (widget.mediaList?.length ?? 0) - 1) {
          Future.delayed(const Duration(milliseconds: 1000), () {
            pageController?.nextPage(
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeInOut,
            );
          });
        }
      }
    });

    mediaPreviewWatch.updateWidget();
  }

  Future<void> loadVideo(String? url, WidgetRef ref) async {
    final mediaPreviewWatch = ref.watch(mediaPreviewController);
    videoPlayerController = VideoPlayerController.file(File(url ?? ''));

    await videoPlayerController?.initialize();
    mediaPreviewWatch.updateWidget();
    chewieController = ChewieController(
      videoPlayerController: videoPlayerController!,
      autoPlay: true,
      looping: false,
      customControls: const Offstage(),
      allowFullScreen: true,
      fullScreenByDefault: true,
    );

    /// Video Player Controller
    videoPlayerController?.setLooping(false);

    mediaPreviewWatch.updateWidget();
    videoPlayerController?.addListener(() {
      if (videoPlayerController?.value.duration == videoPlayerController?.value.position) {
        showLog('Video Ended');
        videoPlayerController?.dispose();
      }
    });
  }
}
